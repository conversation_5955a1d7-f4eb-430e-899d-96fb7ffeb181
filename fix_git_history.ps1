# PowerShell script to remove large file from Git history
Write-Host "Starting Git history cleanup..."

# Step 1: Remove the file from all commits using filter-branch
Write-Host "Step 1: Removing stock_cache.db from Git history..."
git filter-branch --force --index-filter "git rm --cached --ignore-unmatch stock_cache.db" --prune-empty --tag-name-filter cat -- --all

if ($LASTEXITCODE -eq 0) {
    Write-Host "Filter-branch completed successfully"
} else {
    Write-Host "Filter-branch failed, trying alternative method..."
    # Alternative: Use git filter-repo if available
    git filter-repo --path stock_cache.db --invert-paths --force
}

# Step 2: Clean up references
Write-Host "Step 2: Cleaning up references..."
git for-each-ref --format="delete %(refname)" refs/original | git update-ref --stdin

# Step 3: Expire reflog and garbage collect
Write-Host "Step 3: Cleaning up reflog and garbage collection..."
git reflog expire --expire=now --all
git gc --prune=now --aggressive

# Step 4: Verify the file is gone
Write-Host "Step 4: Verifying file removal..."
$fileExists = git rev-list --objects --all | Select-String "stock_cache.db"
if ($fileExists) {
    Write-Host "WARNING: File still exists in history"
} else {
    Write-Host "SUCCESS: File removed from history"
}

# Step 5: Force push
Write-Host "Step 5: Attempting force push..."
git push --force-with-lease origin main

Write-Host "Git history cleanup completed!"
