# PowerShell script to remove large file from Git history - Version 2
Write-Host "Starting Git history cleanup (Version 2)..."

# Step 0: Clean up working directory
Write-Host "Step 0: Cleaning up working directory..."
git status --porcelain

# Check if there are any changes
$changes = git status --porcelain
if ($changes) {
    Write-Host "Found uncommitted changes, stashing them..."
    git stash push -m "Temporary stash before history cleanup"
}

# Step 1: Try to remove the file from history using git filter-branch
Write-Host "Step 1: Attempting to remove stock_cache.db from Git history..."
$env:FILTER_BRANCH_SQUELCH_WARNING = "1"
git filter-branch --force --index-filter "git rm --cached --ignore-unmatch stock_cache.db" --prune-empty --tag-name-filter cat -- --all

if ($LASTEXITCODE -eq 0) {
    Write-Host "Filter-branch completed successfully"
    
    # Step 2: Clean up references
    Write-Host "Step 2: Cleaning up references..."
    git for-each-ref --format="delete %(refname)" refs/original | git update-ref --stdin
    
    # Step 3: Expire reflog and garbage collect
    Write-Host "Step 3: Cleaning up reflog and garbage collection..."
    git reflog expire --expire=now --all
    git gc --prune=now --aggressive
    
    # Step 4: Verify the file is gone
    Write-Host "Step 4: Verifying file removal..."
    $fileCheck = git rev-list --objects --all | Select-String "stock_cache.db"
    if ($fileCheck) {
        Write-Host "WARNING: File still exists in history"
        Write-Host $fileCheck
    } else {
        Write-Host "SUCCESS: File removed from history"
    }
    
    # Step 5: Force push
    Write-Host "Step 5: Attempting force push..."
    git push --force-with-lease origin main
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "SUCCESS: Push completed successfully!"
    } else {
        Write-Host "Push failed. You may need to manually force push."
    }
} else {
    Write-Host "Filter-branch failed. Trying alternative approach..."
    
    # Alternative: Reset to a clean state and try manual approach
    Write-Host "Attempting manual history rewrite..."
    
    # Get the commit before the large file was added
    $commits = git log --oneline --all --grep="stock_cache" -n 5
    Write-Host "Commits mentioning stock_cache:"
    Write-Host $commits
    
    Write-Host "You may need to manually identify and remove the problematic commits."
}

# Restore stashed changes if any
if ($changes) {
    Write-Host "Restoring stashed changes..."
    git stash pop
}

Write-Host "Git history cleanup completed!"
